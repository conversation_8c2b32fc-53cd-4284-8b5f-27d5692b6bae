/* @import "tailwindcss/preflight"; */
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}



@layer base {
  body {
    @apply bg-white text-gray-900 font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

/* Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(20px) translateX(-20px);
  }
}

.animate-fadeIn {
  animation: fadeIn 1s ease-in forwards;
}

.animate-float-slow {
  animation: float-slow 15s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 15s ease-in-out infinite;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1500 {
  animation-delay: 1500ms;
}

.animation-delay-2000 {
  animation-delay: 2000ms;
}

